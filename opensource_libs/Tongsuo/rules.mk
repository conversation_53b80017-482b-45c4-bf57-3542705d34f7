

LOCAL_DIR := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

TONGSUO_DIR := opensource_libs/Tongsuo

include $(TONGSUO_DIR)/sources.mk

# ===== Platform-Specific Source Selection (BoringSSL Style) =====
# Create TEE-optimized source list by filtering out assembly files

# Start with base crypto sources
TONGSUO_TEE_SOURCES := $(tongsuo_crypto_sources)

# Filter out assembly files for TEE environment (similar to BoringSSL's conditional compilation)
# This ensures we use pure C implementations for better compatibility and security
TONGSUO_TEE_SOURCES := $(filter-out crypto/aes/aesv8-armx.S,$(TONGSUO_TEE_SOURCES))
TONGSUO_TEE_SOURCES := $(filter-out crypto/aes/vpaes-armv8.S,$(TONGSUO_TEE_SOURCES))
TONGSUO_TEE_SOURCES := $(filter-out crypto/arm64cpuid.S,$(TONGSUO_TEE_SOURCES))
TONGSUO_TEE_SOURCES := $(filter-out crypto/bn/armv8-mont.S,$(TONGSUO_TEE_SOURCES))
TONGSUO_TEE_SOURCES := $(filter-out crypto/chacha/chacha-armv8.S,$(TONGSUO_TEE_SOURCES))
TONGSUO_TEE_SOURCES := $(filter-out crypto/ec/ecp_nistz256-armv8.S,$(TONGSUO_TEE_SOURCES))
TONGSUO_TEE_SOURCES := $(filter-out crypto/modes/aes-gcm-armv8_64.S,$(TONGSUO_TEE_SOURCES))
TONGSUO_TEE_SOURCES := $(filter-out crypto/modes/ghashv8-armx.S,$(TONGSUO_TEE_SOURCES))
TONGSUO_TEE_SOURCES := $(filter-out crypto/poly1305/poly1305-armv8.S,$(TONGSUO_TEE_SOURCES))
TONGSUO_TEE_SOURCES := $(filter-out crypto/sha/keccak1600-armv8.S,$(TONGSUO_TEE_SOURCES))
TONGSUO_TEE_SOURCES := $(filter-out crypto/sha/sha1-armv8.S,$(TONGSUO_TEE_SOURCES))
TONGSUO_TEE_SOURCES := $(filter-out crypto/sha/sha256-armv8.S,$(TONGSUO_TEE_SOURCES))
TONGSUO_TEE_SOURCES := $(filter-out crypto/sha/sha512-armv8.S,$(TONGSUO_TEE_SOURCES))
TONGSUO_TEE_SOURCES := $(filter-out crypto/sm3/sm3-armv8.S,$(TONGSUO_TEE_SOURCES))
TONGSUO_TEE_SOURCES := $(filter-out crypto/sm4/sm4-armv8.S,$(TONGSUO_TEE_SOURCES))

# Also filter out platform capability detection files that are not needed in TEE
TONGSUO_TEE_SOURCES := $(filter-out crypto/armcap.c,$(TONGSUO_TEE_SOURCES))

# Use filtered source list
MODULE_SRCS := \
	$(addprefix $(TONGSUO_DIR)/,$(TONGSUO_TEE_SOURCES)) \
	$(TONGSUO_DIR)/trusty_stubs.c \

MODULE_INCLUDES += \
	$(TONGSUO_DIR) \
	$(TONGSUO_DIR)/include \
	$(TONGSUO_DIR)/crypto \
	$(TONGSUO_DIR)/providers/common/include \
	$(TONGSUO_DIR)/providers/implementations/include \

MODULE_CFLAGS += \
	-DTONGSUO_IMPLEMENTATION \
	-DOPENSSL_NO_STDIO \
	-DOPENSSL_NO_SOCK \
	-DOPENSSL_NO_THREADS \
	-D__TEE__ \
	-DOPENSSL_NO_ASM \
	-DOPENSSL_NO_ENGINE \
	-DOPENSSL_NO_HW \
	-DOPENSSL_NO_SECURE_MEMORY \
	-DOPENSSL_RAND_SEED_NONE \
	-U__linux \
	-DOPENSSL_SMALL_FOOTPRINT \
	-D__GNUC_PREREQ\(maj,min\)=0 \
	-D__glibc_clang_prereq\(maj,min\)=0 \
	-Wno-unused-parameter \
	-Wno-sign-compare \
	-Wno-unused-function \
	-Wno-unused-variable \
	-Wno-implicit-function-declaration \
	-Wno-incompatible-pointer-types-discards-qualifiers

# Use OpenSSL 1.1.1 compatible API (same as BoringSSL) to avoid Provider system complexity
MODULE_CFLAGS += \
	-DOPENSSL_API_COMPAT=0x10101000L \
	-DOPENSSL_NO_DEPRECATED

# Use OpenSSL 1.1.1 compatible API to avoid Provider system complexity
MODULE_CFLAGS += \
	-DOPENSSL_API_COMPAT=0x10101000L \
	-DOPENSSL_NO_DEPRECATED \

MODULE_ASFLAGS += \
	-Wno-unused-parameter \

# Symbol prefix isolation to avoid conflicts with BoringSSL
MODULE_CFLAGS += -DSYMBOL_PREFIX=TONGSUO_

# Disable features not needed in TEE environment
MODULE_CFLAGS += \
	-DOPENSSL_NO_APPS \
	-DOPENSSL_NO_ASYNC \
	-DOPENSSL_NO_AUTOLOAD_CONFIG \
	-DOPENSSL_NO_DGRAM \
	-DOPENSSL_NO_DSO \
	-DOPENSSL_NO_DTLS \
	-DOPENSSL_NO_ENGINE \
	-DOPENSSL_NO_FIPS \
	-DOPENSSL_NO_SHARED \
	-DOPENSSL_NO_SSL \
	-DOPENSSL_NO_POSIX_IO \
	-DOPENSSL_NO_SOCK \
	-DOPENSSL_NO_DGRAM \
	-DOPENSSL_NO_SCTP \
	-DOPENSSL_NO_SPEED \
	-DOPENSSL_NO_TESTS \
	-DOPENSSL_NO_UNIT_TEST \
	-DOPENSSL_NO_DYNAMIC_ENGINE \
	-DOPENSSL_NO_STATIC_ENGINE \
	-DOPENSSL_NO_AUTOERRINIT \
	-DOPENSSL_NO_ERR \
	-DOPENSSL_NO_FILENAMES \
	-DOPENSSL_NO_STDIO \
	-DOPENSSL_NO_BIO \
	-DOPENSSL_NO_SOCK \
	-DOPENSSL_NO_DGRAM \
	-DOPENSSL_CPUID_OBJ= \
	-DOPENSSL_NO_CPUID \
	-DOPENSSL_NO_INLINE_ASM \
	-DOPENSSL_NO_BLAKE2 \
	-DOPENSSL_NO_WHIRLPOOL \
	-DOPENSSL_NO_GOST \
	-DOPENSSL_NO_IDEA \
	-DOPENSSL_NO_SEED \
	-DOPENSSL_NO_CAMELLIA \
	-DOPENSSL_NO_CAST \
	-DOPENSSL_NO_BF \
	-DOPENSSL_NO_RC2 \
	-DOPENSSL_NO_RC5 \
	-DOPENSSL_NO_MDC2 \
	-DOPENSSL_NO_MD4 \
	-DOPENSSL_NO_RMD160 \
	-DOPENSSL_NO_TESTS \
	-DOPENSSL_NO_TLS \
	-DOPENSSL_NO_UI_CONSOLE \
	-DOPENSSL_STATIC_ARMCAP \
	-D__TRUSTY__ \
	-DOPENSSL_NO_PROVIDER \
	-DOPENSSL_NO_LEGACY \

# Enable SM algorithms
MODULE_CFLAGS += \
	-DOPENSSL_ENABLE_SM2 \
	-DOPENSSL_ENABLE_SM3 \
	-DOPENSSL_ENABLE_SM4 \

# Define required directory macros for TEE environment
MODULE_CFLAGS += \
	-DOPENSSLDIR=\"/tee/tongsuo\" \
	-DENGINESDIR=\"/tee/tongsuo/engines\" \
	-DMODULESDIR=\"/tee/tongsuo/modules\" \

include make/rctee_lib.mk
