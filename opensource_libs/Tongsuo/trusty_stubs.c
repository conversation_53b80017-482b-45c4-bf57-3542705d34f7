/*
 * Trusty environment stubs for Tongsuo
 * Provides empty implementations for system calls not available in TEE
 */

#include <stdint.h>
#include <signal.h>
#include <setjmp.h>
#include <string.h>
#include <stddef.h>

// Signal handling stubs - not available in TEE environment
int sigfillset(sigset_t *set) {
    (void)set;
    return 0;
}

int sigdelset(sigset_t *set, int signum) {
    (void)set;
    (void)signum;
    return 0;
}

int sigprocmask(int how, const sigset_t *set, sigset_t *oldset) {
    (void)how;
    (void)set;
    (void)oldset;
    return 0;
}

int sigaction(int signum, const struct sigaction *act, struct sigaction *oldact) {
    (void)signum;
    (void)act;
    (void)oldact;
    return 0;
}

// setjmp/longjmp stubs - simplified for TEE
int sigsetjmp(sigjmp_buf env, int savemask) {
    (void)env;
    (void)savemask;
    return 0;
}

// ARM capability probe functions - return 0 (feature not available)
void _armv7_neon_probe(void) {
    // Empty stub
}

void _armv8_pmull_probe(void) {
    // Empty stub
}

void _armv8_aes_probe(void) {
    // Empty stub
}

void _armv8_sha1_probe(void) {
    // Empty stub
}

void _armv8_sha256_probe(void) {
    // Empty stub
}

void _armv8_sm4_probe(void) {
    // Empty stub
}

void _armv8_sha512_probe(void) {
    // Empty stub
}

// Tongsuo-specific probe functions with symbol prefix
void TONGSUO__armv7_neon_probe(void) {
    // Empty stub
}

void TONGSUO__armv8_pmull_probe(void) {
    // Empty stub
}

void TONGSUO__armv8_aes_probe(void) {
    // Empty stub
}

void TONGSUO__armv8_sha1_probe(void) {
    // Empty stub
}

void TONGSUO__armv8_sha256_probe(void) {
    // Empty stub
}

void TONGSUO__armv8_sm4_probe(void) {
    // Empty stub
}

void TONGSUO__armv8_sha512_probe(void) {
    // Empty stub
}

void TONGSUO__armv7_tick(void) {
    // Empty stub
}

void TONGSUO__armv8_sm3_probe(void) {
    // Empty stub
}

void TONGSUO__armv8_cpuid_probe(void) {
    // Empty stub
}

// Additional system call stubs
void siglongjmp(sigjmp_buf env, int val) {
    (void)env;
    (void)val;
    // Empty stub - should not be called in TEE
}

// User/group ID stubs - return safe defaults
unsigned int getuid(void) {
    return 0;
}

unsigned int geteuid(void) {
    return 0;
}

unsigned int getgid(void) {
    return 0;
}

unsigned int getegid(void) {
    return 0;
}

// Crypto function stubs
void TONGSUO_SHA3_absorb(void *ctx, const void *inp, size_t len, size_t r) {
    (void)ctx;
    (void)inp;
    (void)len;
    (void)r;
    // Empty stub
}

void TONGSUO_SHA3_squeeze(void *ctx, void *out, size_t len, size_t r) {
    (void)ctx;
    (void)out;
    (void)len;
    (void)r;
    // Empty stub
}

int TONGSUO_CRYPTO_memcmp(const void *a, const void *b, size_t len) {
    // Use standard memcmp
    return memcmp(a, b, len);
}

// Curve448 stubs
void TONGSUO_ossl_x448_public_from_private(void *pub, const void *priv) {
    (void)pub;
    (void)priv;
    // Empty stub
}

void TONGSUO_ossl_ed448_public_from_private(void *pub, const void *priv) {
    (void)pub;
    (void)priv;
    // Empty stub
}

// ARM crypto assembly function stubs
void TONGSUO_sm4_v8_set_encrypt_key(const unsigned char *userKey, void *key) {
    (void)userKey;
    (void)key;
    // Empty stub
}

void TONGSUO_sm4_v8_set_decrypt_key(const unsigned char *userKey, void *key) {
    (void)userKey;
    (void)key;
    // Empty stub
}

void TONGSUO_sm4_v8_encrypt(const unsigned char *in, unsigned char *out, const void *key) {
    (void)in;
    (void)out;
    (void)key;
    // Empty stub
}

void TONGSUO_sm4_v8_decrypt(const unsigned char *in, unsigned char *out, const void *key) {
    (void)in;
    (void)out;
    (void)key;
    // Empty stub
}

void TONGSUO_sm4_v8_cbc_encrypt(const unsigned char *in, unsigned char *out, size_t length, const void *key, unsigned char *ivec, int enc) {
    (void)in;
    (void)out;
    (void)length;
    (void)key;
    (void)ivec;
    (void)enc;
    // Empty stub
}

int TONGSUO_aes_v8_set_decrypt_key(const unsigned char *userKey, int bits, void *key) {
    (void)userKey;
    (void)bits;
    (void)key;
    return 0;
}

void TONGSUO_aes_v8_decrypt(const unsigned char *in, unsigned char *out, const void *key) {
    (void)in;
    (void)out;
    (void)key;
    // Empty stub
}

void TONGSUO_aes_v8_cbc_encrypt(const unsigned char *in, unsigned char *out, size_t length, const void *key, unsigned char *ivec, int enc) {
    (void)in;
    (void)out;
    (void)length;
    (void)key;
    (void)ivec;
    (void)enc;
    // Empty stub
}

int TONGSUO_aes_v8_set_encrypt_key(const unsigned char *userKey, int bits, void *key) {
    (void)userKey;
    (void)bits;
    (void)key;
    return 0;
}

void TONGSUO_aes_v8_encrypt(const unsigned char *in, unsigned char *out, const void *key) {
    (void)in;
    (void)out;
    (void)key;
    // Empty stub
}

void TONGSUO_ChaCha20_ctr32(unsigned char *out, const unsigned char *inp, size_t len, const unsigned int key[8], const unsigned int counter[4]) {
    (void)out;
    (void)inp;
    (void)len;
    (void)key;
    (void)counter;
    // Empty stub
}

void TONGSUO_gcm_ghash_v8(unsigned long long Xi[2], const void *Htable, const unsigned char *inp, size_t len) {
    (void)Xi;
    (void)Htable;
    (void)inp;
    (void)len;
    // Empty stub
}

// Additional ARM GCM functions
void TONGSUO_gcm_init_v8(void *Htable, const unsigned long long H[2]) {
    (void)Htable;
    (void)H;
    // Empty stub
}

void TONGSUO_armv8_aes_gcm_encrypt(const unsigned char *in, unsigned char *out, size_t len, const void *key, unsigned char ivec[16], void *Xi) {
    (void)in;
    (void)out;
    (void)len;
    (void)key;
    (void)ivec;
    (void)Xi;
    // Empty stub
}

void TONGSUO_armv8_aes_gcm_decrypt(const unsigned char *in, unsigned char *out, size_t len, const void *key, unsigned char ivec[16], void *Xi) {
    (void)in;
    (void)out;
    (void)len;
    (void)key;
    (void)ivec;
    (void)Xi;
    // Empty stub
}

// Provider-related stubs
void* TONGSUO_ossl_pool_acquire_entropy(void *pool) {
    (void)pool;
    return NULL;
}

void* TONGSUO_ossl_prov_bio_from_dispatch(void *provctx) {
    (void)provctx;
    return NULL;
}

void* TONGSUO_ossl_prov_ctx_new(void) {
    return NULL;
}

int TONGSUO_ossl_bio_prov_init_bio_method(void) {
    return 0;
}

int TONGSUO_ossl_pool_add_nonce_data(void *pool) {
    (void)pool;
    return 0;
}

int TONGSUO_ossl_prov_seeding_from_dispatch(void *provctx) {
    (void)provctx;
    return 0;
}

void TONGSUO_ossl_prov_ctx_free(void *ctx) {
    (void)ctx;
    // Empty stub
}

void TONGSUO_ossl_prov_ctx_set0_libctx(void *ctx, void *libctx) {
    (void)ctx;
    (void)libctx;
    // Empty stub
}

void TONGSUO_ossl_prov_ctx_set0_handle(void *ctx, void *handle) {
    (void)ctx;
    (void)handle;
    // Empty stub
}

// Additional SM4 functions
void TONGSUO_sm4_v8_ecb_encrypt(const unsigned char *in, unsigned char *out, size_t length, const void *key, int enc) {
    (void)in;
    (void)out;
    (void)length;
    (void)key;
    (void)enc;
    // Empty stub
}

// Provider core functions
void TONGSUO_ossl_prov_ctx_set0_core_bio_method(void *ctx, void *method) {
    (void)ctx;
    (void)method;
    // Empty stub
}

void* TONGSUO_ossl_prov_ctx_get0_core_bio_method(void *ctx) {
    (void)ctx;
    return NULL;
}

int TONGSUO_ossl_prov_cache_exported_algorithms(void *ctx) {
    (void)ctx;
    return 0;
}

void* TONGSUO_ossl_prov_get_capabilities(void *ctx) {
    (void)ctx;
    return NULL;
}

// Cipher function tables - return NULL for all
void* TONGSUO_ossl_null_functions = NULL;
void* TONGSUO_ossl_aes256ecb_functions = NULL;
void* TONGSUO_ossl_aes192ecb_functions = NULL;
void* TONGSUO_ossl_aes128ecb_functions = NULL;
void* TONGSUO_ossl_aes256cbc_functions = NULL;
void* TONGSUO_ossl_aes192cbc_functions = NULL;
void* TONGSUO_ossl_aes128cbc_functions = NULL;

// Encoder functions
void* TONGSUO_ossl_rsa_to_text_encoder_functions = NULL;

// Additional AES XTS functions (ARM specific)
void TONGSUO_aes_v8_xts_encrypt(const unsigned char *in, unsigned char *out, size_t length, const void *key1, const void *key2, unsigned char ivec[16]) {
    (void)in;
    (void)out;
    (void)length;
    (void)key1;
    (void)key2;
    (void)ivec;
    // Empty stub
}

void TONGSUO_aes_v8_xts_decrypt(const unsigned char *in, unsigned char *out, size_t length, const void *key1, const void *key2, unsigned char ivec[16]) {
    (void)in;
    (void)out;
    (void)length;
    (void)key1;
    (void)key2;
    (void)ivec;
    // Empty stub
}

// Random pool management functions
void TONGSUO_ossl_rand_pool_cleanup(void *pool) {
    (void)pool;
    // Empty stub
}

// Additional cipher function tables
void* TONGSUO_ossl_aes128cbc_cts_functions = NULL;
void* TONGSUO_ossl_aes192cbc_cts_functions = NULL;
void* TONGSUO_ossl_aes256cbc_cts_functions = NULL;
void* TONGSUO_ossl_aes256ofb_functions = NULL;
void* TONGSUO_ossl_aes192ofb_functions = NULL;
void* TONGSUO_ossl_aes128ofb_functions = NULL;
void* TONGSUO_ossl_aes256cfb_functions = NULL;
void* TONGSUO_ossl_aes192cfb_functions = NULL;
void* TONGSUO_ossl_aes128cfb_functions = NULL;
void* TONGSUO_ossl_aes256cfb1_functions = NULL;
void* TONGSUO_ossl_aes192cfb1_functions = NULL;
void* TONGSUO_ossl_aes128cfb1_functions = NULL;
void* TONGSUO_ossl_aes256cfb8_functions = NULL;
void* TONGSUO_ossl_aes192cfb8_functions = NULL;
void* TONGSUO_ossl_aes128cfb8_functions = NULL;

// Additional cipher mode functions
void* TONGSUO_ossl_aes256ctr_functions = NULL;
void* TONGSUO_ossl_aes192ctr_functions = NULL;
void* TONGSUO_ossl_aes128ctr_functions = NULL;
void* TONGSUO_ossl_aes256gcm_functions = NULL;
void* TONGSUO_ossl_aes192gcm_functions = NULL;
void* TONGSUO_ossl_aes128gcm_functions = NULL;

// ChaCha20 functions
void* TONGSUO_ossl_chacha20_functions = NULL;
void* TONGSUO_ossl_chacha20_poly1305_functions = NULL;

// DES functions
void* TONGSUO_ossl_des_ede3_ecb_functions = NULL;
void* TONGSUO_ossl_des_ede3_cbc_functions = NULL;
void* TONGSUO_ossl_des_ede_ecb_functions = NULL;
void* TONGSUO_ossl_des_ede_cbc_functions = NULL;

// SM4 functions
void* TONGSUO_ossl_sm4_ecb_functions = NULL;
void* TONGSUO_ossl_sm4_cbc_functions = NULL;
void* TONGSUO_ossl_sm4_cfb_functions = NULL;
void* TONGSUO_ossl_sm4_ofb_functions = NULL;
void* TONGSUO_ossl_sm4_ctr_functions = NULL;

// Digest function tables
void* TONGSUO_ossl_sha1_functions = NULL;
void* TONGSUO_ossl_sha224_functions = NULL;
void* TONGSUO_ossl_sha256_functions = NULL;
void* TONGSUO_ossl_sha384_functions = NULL;
void* TONGSUO_ossl_sha512_functions = NULL;
void* TONGSUO_ossl_sha512_224_functions = NULL;
void* TONGSUO_ossl_sha512_256_functions = NULL;
void* TONGSUO_ossl_sha3_224_functions = NULL;
void* TONGSUO_ossl_sha3_256_functions = NULL;
void* TONGSUO_ossl_sha3_384_functions = NULL;
void* TONGSUO_ossl_sha3_512_functions = NULL;
void* TONGSUO_ossl_sm3_functions = NULL;
void* TONGSUO_ossl_md5_functions = NULL;
void* TONGSUO_ossl_md5_sha1_functions = NULL;

// MAC function tables
void* TONGSUO_ossl_hmac_functions = NULL;
void* TONGSUO_ossl_cmac_functions = NULL;
void* TONGSUO_ossl_gmac_functions = NULL;
void* TONGSUO_ossl_kmac128_functions = NULL;
void* TONGSUO_ossl_kmac256_functions = NULL;

// Key derivation function tables
void* TONGSUO_ossl_hkdf_functions = NULL;
void* TONGSUO_ossl_kbkdf_functions = NULL;
void* TONGSUO_ossl_pbkdf2_functions = NULL;
void* TONGSUO_ossl_scrypt_functions = NULL;
void* TONGSUO_ossl_tls1_prf_functions = NULL;

// Key exchange function tables
void* TONGSUO_ossl_dh_functions = NULL;
void* TONGSUO_ossl_dhx_functions = NULL;
void* TONGSUO_ossl_ecdh_functions = NULL;
void* TONGSUO_ossl_x25519_functions = NULL;
void* TONGSUO_ossl_x448_functions = NULL;

// Signature function tables
void* TONGSUO_ossl_rsa_signature_functions = NULL;
void* TONGSUO_ossl_ecdsa_signature_functions = NULL;
void* TONGSUO_ossl_dsa_signature_functions = NULL;
void* TONGSUO_ossl_ed25519_signature_functions = NULL;
void* TONGSUO_ossl_ed448_signature_functions = NULL;
void* TONGSUO_ossl_sm2_signature_functions = NULL;

// Key management function tables
void* TONGSUO_ossl_rsa_keymgmt_functions = NULL;
void* TONGSUO_ossl_ec_keymgmt_functions = NULL;
void* TONGSUO_ossl_dsa_keymgmt_functions = NULL;
void* TONGSUO_ossl_dh_keymgmt_functions = NULL;
void* TONGSUO_ossl_x25519_keymgmt_functions = NULL;
void* TONGSUO_ossl_x448_keymgmt_functions = NULL;
void* TONGSUO_ossl_ed25519_keymgmt_functions = NULL;
void* TONGSUO_ossl_ed448_keymgmt_functions = NULL;

// Random number generation functions
void* TONGSUO_ossl_seed_src_functions = NULL;
void* TONGSUO_ossl_test_rng_functions = NULL;

// Store function tables
void* TONGSUO_ossl_file_store_functions = NULL;

// Thread management functions (similar to BoringSSL thread_none.c)
void TONGSUO_CRYPTO_MUTEX_init(void *lock) {
    (void)lock;
    // Empty stub - no threading in TEE
}

void TONGSUO_CRYPTO_MUTEX_lock_read(void *lock) {
    (void)lock;
    // Empty stub - no threading in TEE
}

void TONGSUO_CRYPTO_MUTEX_lock_write(void *lock) {
    (void)lock;
    // Empty stub - no threading in TEE
}

void TONGSUO_CRYPTO_MUTEX_unlock_read(void *lock) {
    (void)lock;
    // Empty stub - no threading in TEE
}

void TONGSUO_CRYPTO_MUTEX_unlock_write(void *lock) {
    (void)lock;
    // Empty stub - no threading in TEE
}

void TONGSUO_CRYPTO_MUTEX_cleanup(void *lock) {
    (void)lock;
    // Empty stub - no threading in TEE
}

static int g_once_flag = 0;
void TONGSUO_CRYPTO_once(int *once, void (*init)(void)) {
    if (*once) {
        return;
    }
    *once = 1;
    init();
}

// Thread local storage stubs
static void *g_thread_locals[16]; // Assume max 16 thread locals

void* TONGSUO_CRYPTO_get_thread_local(int index) {
    if (index < 0 || index >= 16) {
        return NULL;
    }
    return g_thread_locals[index];
}

int TONGSUO_CRYPTO_set_thread_local(int index, void *value, void (*destructor)(void*)) {
    (void)destructor; // Ignore destructor in TEE
    if (index < 0 || index >= 16) {
        return 0;
    }
    g_thread_locals[index] = value;
    return 1;
}

// Random number generation functions (similar to BoringSSL trusty.c)
void TONGSUO_CRYPTO_sysrand(unsigned char *out, size_t requested) {
    // Use simple counter for testing - in real implementation should use hardware RNG
    static unsigned int counter = 0;
    for (size_t i = 0; i < requested; i++) {
        out[i] = (unsigned char)(counter++ & 0xFF);
    }
}

void TONGSUO_CRYPTO_sysrand_for_seed(unsigned char *out, size_t requested) {
    TONGSUO_CRYPTO_sysrand(out, requested);
}

// Note: Error handling and BIO functions are already implemented in Tongsuo

// File I/O stubs (not supported in TEE)
void* TONGSUO_fopen(const char *filename, const char *mode) {
    (void)filename;
    (void)mode;
    return NULL; // No file I/O in TEE
}

int TONGSUO_fclose(void *fp) {
    (void)fp;
    return 0;
}

size_t TONGSUO_fread(void *ptr, size_t size, size_t nmemb, void *stream) {
    (void)ptr;
    (void)size;
    (void)nmemb;
    (void)stream;
    return 0;
}

size_t TONGSUO_fwrite(const void *ptr, size_t size, size_t nmemb, void *stream) {
    (void)ptr;
    (void)size;
    (void)nmemb;
    (void)stream;
    return 0;
}

// Additional encoder function tables
void* TONGSUO_ossl_ec_to_text_encoder_functions = NULL;
void* TONGSUO_ossl_dsa_to_text_encoder_functions = NULL;
void* TONGSUO_ossl_dh_to_text_encoder_functions = NULL;
void* TONGSUO_ossl_ed25519_to_text_encoder_functions = NULL;
void* TONGSUO_ossl_ed448_to_text_encoder_functions = NULL;
void* TONGSUO_ossl_x25519_to_text_encoder_functions = NULL;
void* TONGSUO_ossl_x448_to_text_encoder_functions = NULL;
void* TONGSUO_ossl_sm2_to_text_encoder_functions = NULL;

// Type-specific encoder functions
void* TONGSUO_ossl_rsa_to_type_specific_keypair_der_encoder_functions = NULL;
void* TONGSUO_ossl_rsa_to_type_specific_keypair_pem_encoder_functions = NULL;
void* TONGSUO_ossl_dh_to_type_specific_params_der_encoder_functions = NULL;
void* TONGSUO_ossl_dh_to_type_specific_params_pem_encoder_functions = NULL;

// XTS mode functions
void* TONGSUO_ossl_aes256xts_functions = NULL;
void* TONGSUO_ossl_aes128xts_functions = NULL;

// Additional type-specific encoder functions
void* TONGSUO_ossl_ec_to_type_specific_der_encoder_functions = NULL;
void* TONGSUO_ossl_ec_to_type_specific_pem_encoder_functions = NULL;
void* TONGSUO_ossl_dsa_to_type_specific_der_encoder_functions = NULL;
void* TONGSUO_ossl_dsa_to_type_specific_pem_encoder_functions = NULL;
void* TONGSUO_ossl_ec_to_type_specific_no_pub_der_encoder_functions = NULL;
void* TONGSUO_ossl_ec_to_type_specific_no_pub_pem_encoder_functions = NULL;
void* TONGSUO_ossl_ec_to_blob_encoder_functions = NULL;
void* TONGSUO_ossl_sm2_to_type_specific_no_pub_der_encoder_functions = NULL;
void* TONGSUO_ossl_sm2_to_type_specific_no_pub_pem_encoder_functions = NULL;
void* TONGSUO_ossl_sm2_to_blob_encoder_functions = NULL;

// Microsoft-specific encoder functions
void* TONGSUO_ossl_rsa_to_msblob_encoder_functions = NULL;
void* TONGSUO_ossl_rsa_to_pvk_encoder_functions = NULL;
void* TONGSUO_ossl_dsa_to_msblob_encoder_functions = NULL;
void* TONGSUO_ossl_dsa_to_pvk_encoder_functions = NULL;

// Additional missing encoder functions
void* TONGSUO_ossl_ec_to_type_specific_keypair_der_encoder_functions = NULL;
void* TONGSUO_ossl_ec_to_type_specific_keypair_pem_encoder_functions = NULL;
void* TONGSUO_ossl_dsa_to_type_specific_keypair_der_encoder_functions = NULL;
void* TONGSUO_ossl_dsa_to_type_specific_keypair_pem_encoder_functions = NULL;
void* TONGSUO_ossl_sm2_to_type_specific_der_encoder_functions = NULL;
void* TONGSUO_ossl_sm2_to_type_specific_pem_encoder_functions = NULL;
void* TONGSUO_ossl_sm2_to_type_specific_keypair_der_encoder_functions = NULL;
void* TONGSUO_ossl_sm2_to_type_specific_keypair_pem_encoder_functions = NULL;

// Additional algorithm encoder functions
void* TONGSUO_ossl_ed25519_to_type_specific_der_encoder_functions = NULL;
void* TONGSUO_ossl_ed25519_to_type_specific_pem_encoder_functions = NULL;
void* TONGSUO_ossl_ed448_to_type_specific_der_encoder_functions = NULL;
void* TONGSUO_ossl_ed448_to_type_specific_pem_encoder_functions = NULL;
void* TONGSUO_ossl_x25519_to_type_specific_der_encoder_functions = NULL;
void* TONGSUO_ossl_x25519_to_type_specific_pem_encoder_functions = NULL;
void* TONGSUO_ossl_x448_to_type_specific_der_encoder_functions = NULL;
void* TONGSUO_ossl_x448_to_type_specific_pem_encoder_functions = NULL;

// RSA encoder functions
void* TONGSUO_ossl_rsa_to_EncryptedPrivateKeyInfo_der_encoder_functions = NULL;
void* TONGSUO_ossl_rsa_to_EncryptedPrivateKeyInfo_pem_encoder_functions = NULL;
void* TONGSUO_ossl_rsa_to_PrivateKeyInfo_der_encoder_functions = NULL;
void* TONGSUO_ossl_rsa_to_PrivateKeyInfo_pem_encoder_functions = NULL;
void* TONGSUO_ossl_rsa_to_SubjectPublicKeyInfo_der_encoder_functions = NULL;
void* TONGSUO_ossl_rsa_to_SubjectPublicKeyInfo_pem_encoder_functions = NULL;

// RSA-PSS encoder functions
void* TONGSUO_ossl_rsapss_to_EncryptedPrivateKeyInfo_der_encoder_functions = NULL;
void* TONGSUO_ossl_rsapss_to_EncryptedPrivateKeyInfo_pem_encoder_functions = NULL;
void* TONGSUO_ossl_rsapss_to_PrivateKeyInfo_der_encoder_functions = NULL;
void* TONGSUO_ossl_rsapss_to_PrivateKeyInfo_pem_encoder_functions = NULL;

// OCB mode functions
void* TONGSUO_ossl_aes256ocb_functions = NULL;
void* TONGSUO_ossl_aes192ocb_functions = NULL;
void* TONGSUO_ossl_aes128ocb_functions = NULL;

// DH/DHX encoder functions
void* TONGSUO_ossl_dhx_to_type_specific_params_pem_encoder_functions = NULL;
void* TONGSUO_ossl_dh_to_EncryptedPrivateKeyInfo_der_encoder_functions = NULL;
void* TONGSUO_ossl_dh_to_EncryptedPrivateKeyInfo_pem_encoder_functions = NULL;
void* TONGSUO_ossl_dh_to_PrivateKeyInfo_der_encoder_functions = NULL;

// SIV mode functions
void* TONGSUO_ossl_aes128siv_functions = NULL;
void* TONGSUO_ossl_aes192siv_functions = NULL;
void* TONGSUO_ossl_aes256siv_functions = NULL;

// CCM mode functions
void* TONGSUO_ossl_aes256ccm_functions = NULL;
void* TONGSUO_ossl_aes192ccm_functions = NULL;
void* TONGSUO_ossl_aes128ccm_functions = NULL;

// WRAP mode functions
void* TONGSUO_ossl_aes256wrap_functions = NULL;
void* TONGSUO_ossl_aes192wrap_functions = NULL;
void* TONGSUO_ossl_aes128wrap_functions = NULL;

// Additional RSA-PSS encoder functions
void* TONGSUO_ossl_rsapss_to_SubjectPublicKeyInfo_der_encoder_functions = NULL;
void* TONGSUO_ossl_rsapss_to_SubjectPublicKeyInfo_pem_encoder_functions = NULL;

// DH/DHX additional encoder functions
void* TONGSUO_ossl_dh_to_PrivateKeyInfo_pem_encoder_functions = NULL;
void* TONGSUO_ossl_dh_to_SubjectPublicKeyInfo_der_encoder_functions = NULL;
void* TONGSUO_ossl_dh_to_SubjectPublicKeyInfo_pem_encoder_functions = NULL;
void* TONGSUO_ossl_dhx_to_EncryptedPrivateKeyInfo_der_encoder_functions = NULL;
void* TONGSUO_ossl_dhx_to_EncryptedPrivateKeyInfo_pem_encoder_functions = NULL;
void* TONGSUO_ossl_dhx_to_PrivateKeyInfo_der_encoder_functions = NULL;
void* TONGSUO_ossl_dhx_to_PrivateKeyInfo_pem_encoder_functions = NULL;
void* TONGSUO_ossl_dhx_to_SubjectPublicKeyInfo_der_encoder_functions = NULL;
void* TONGSUO_ossl_dhx_to_SubjectPublicKeyInfo_pem_encoder_functions = NULL;

// DSA additional encoder functions
void* TONGSUO_ossl_dsa_to_EncryptedPrivateKeyInfo_der_encoder_functions = NULL;
void* TONGSUO_ossl_dsa_to_EncryptedPrivateKeyInfo_pem_encoder_functions = NULL;
void* TONGSUO_ossl_dsa_to_PrivateKeyInfo_der_encoder_functions = NULL;

// Additional WRAP mode functions
void* TONGSUO_ossl_aes256wrappad_functions = NULL;
void* TONGSUO_ossl_aes192wrappad_functions = NULL;
void* TONGSUO_ossl_aes128wrappad_functions = NULL;

// Additional text encoder functions
void* TONGSUO_ossl_rsapss_to_text_encoder_functions = NULL;
void* TONGSUO_ossl_dhx_to_text_encoder_functions = NULL;
void* TONGSUO_ossl_dhx_to_type_specific_params_der_encoder_functions = NULL;

// WRAP inverse mode functions
void* TONGSUO_ossl_aes256wrapinv_functions = NULL;
void* TONGSUO_ossl_aes192wrapinv_functions = NULL;
void* TONGSUO_ossl_aes128wrapinv_functions = NULL;
void* TONGSUO_ossl_aes256wrappadinv_functions = NULL;
void* TONGSUO_ossl_aes192wrappadinv_functions = NULL;
void* TONGSUO_ossl_aes128wrappadinv_functions = NULL;

// CBC-HMAC combined mode functions
void* TONGSUO_ossl_aes128cbc_hmac_sha1_functions = NULL;
void* TONGSUO_ossl_aes256cbc_hmac_sha1_functions = NULL;
void* TONGSUO_ossl_aes128cbc_hmac_sha256_functions = NULL;

// Capability check functions
int TONGSUO_ossl_cipher_capable_aes_cbc_hmac_sha1(void) {
    return 0; // Not supported in TEE
}
